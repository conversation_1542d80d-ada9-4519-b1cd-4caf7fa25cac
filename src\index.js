import express from "express";
import cors from "cors";
import "dotenv/config";
import job from "./libs/cron.js";

import authRoutes from "./routers/authRoutes.js";
import bookRoutes from "./routers/bookRoutes.js";

import { connectDB } from "./libs/db.js";

const app = express();
const PORT = process.env.PORT || 3000;

job.start();
app.use(express.json());
app.use(cors());

app.use("/api/auth", authRoutes);
app.use("/api/books", bookRoutes);

app.get("/", (req, res) => {
  res.send("Hello World!");
});

app.listen(PORT, () => {
  console.log(`Server running on port http://localhost:${PORT}`);
  connectDB();
});
